<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="知识库名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入知识库名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="知识库类型" prop="type">
        <el-select v-model="queryParams.type" placeholder="请选择知识库类型" clearable>
          <el-option label="通用" value="general" />
          <el-option label="专业" value="professional" />
          <el-option label="个人" value="personal" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="知识库状态" clearable>
          <el-option label="正常" value="0" />
          <el-option label="停用" value="1" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['knowledge:my:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['knowledge:my:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['knowledge:my:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['knowledge:my:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 标签页切换 -->
    <el-tabs v-model="activeTab" @tab-click="handleTabClick">
      <el-tab-pane label="全部知识库" name="all">
        <el-table v-loading="loading" :data="knowledgeBaseList" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="知识库ID" align="center" prop="id" />
          <el-table-column label="知识库名称" align="center" prop="name" :show-overflow-tooltip="true" />
          <el-table-column label="描述" align="center" prop="description" :show-overflow-tooltip="true" />
          <el-table-column label="类型" align="center" prop="type" />
          <el-table-column label="状态" align="center" prop="status">
            <template #default="scope">
              <dict-tag :options="sys_normal_disable" :value="scope.row.status"/>
            </template>
          </el-table-column>
          <el-table-column label="文档数量" align="center" prop="documentCount" />
          <el-table-column label="创建者" align="center" prop="creatorName" />
          <el-table-column label="创建时间" align="center" prop="createTime" width="180">
            <template #default="scope">
              <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template #default="scope">
              <el-button link type="primary" icon="View" @click="handleDetail(scope.row)" v-hasPermi="['knowledge:my:query']">详情</el-button>
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['knowledge:my:edit']">修改</el-button>
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['knowledge:my:remove']">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
      
      <el-tab-pane label="我创建的" name="created">
        <el-table v-loading="loading" :data="knowledgeBaseList" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="知识库ID" align="center" prop="id" />
          <el-table-column label="知识库名称" align="center" prop="name" :show-overflow-tooltip="true" />
          <el-table-column label="描述" align="center" prop="description" :show-overflow-tooltip="true" />
          <el-table-column label="类型" align="center" prop="type" />
          <el-table-column label="状态" align="center" prop="status">
            <template #default="scope">
              <dict-tag :options="sys_normal_disable" :value="scope.row.status"/>
            </template>
          </el-table-column>
          <el-table-column label="文档数量" align="center" prop="documentCount" />
          <el-table-column label="创建时间" align="center" prop="createTime" width="180">
            <template #default="scope">
              <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template #default="scope">
              <el-button link type="primary" icon="View" @click="handleDetail(scope.row)" v-hasPermi="['knowledge:my:query']">详情</el-button>
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['knowledge:my:edit']">修改</el-button>
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['knowledge:my:remove']">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
      
      <el-tab-pane label="共享给我的" name="shared">
        <el-table v-loading="loading" :data="knowledgeBaseList" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="知识库ID" align="center" prop="id" />
          <el-table-column label="知识库名称" align="center" prop="name" :show-overflow-tooltip="true" />
          <el-table-column label="描述" align="center" prop="description" :show-overflow-tooltip="true" />
          <el-table-column label="类型" align="center" prop="type" />
          <el-table-column label="状态" align="center" prop="status">
            <template #default="scope">
              <dict-tag :options="sys_normal_disable" :value="scope.row.status"/>
            </template>
          </el-table-column>
          <el-table-column label="文档数量" align="center" prop="documentCount" />
          <el-table-column label="创建者" align="center" prop="creatorName" />
          <el-table-column label="创建时间" align="center" prop="createTime" width="180">
            <template #default="scope">
              <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template #default="scope">
              <el-button link type="primary" icon="View" @click="handleDetail(scope.row)" v-hasPermi="['knowledge:my:query']">详情</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
    </el-tabs>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改知识库对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="knowledgeBaseRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="知识库名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入知识库名称" />
        </el-form-item>
        <el-form-item label="知识库描述" prop="description">
          <el-input v-model="form.description" type="textarea" placeholder="请输入知识库描述" />
        </el-form-item>
        <el-form-item label="知识库类型" prop="type">
          <el-select v-model="form.type" placeholder="请选择知识库类型">
            <el-option label="通用" value="general" />
            <el-option label="专业" value="professional" />
            <el-option label="个人" value="personal" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio label="0">正常</el-radio>
            <el-radio label="1">停用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 知识库详情对话框 -->
    <el-dialog title="知识库详情" v-model="detailOpen" width="600px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="知识库ID">{{ detailData.id }}</el-descriptions-item>
        <el-descriptions-item label="知识库名称">{{ detailData.name }}</el-descriptions-item>
        <el-descriptions-item label="知识库类型">{{ detailData.type }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <dict-tag :options="sys_normal_disable" :value="detailData.status"/>
        </el-descriptions-item>
        <el-descriptions-item label="文档数量">{{ detailData.documentCount }}</el-descriptions-item>
        <el-descriptions-item label="知识库大小">{{ formatBytes(detailData.size) }}</el-descriptions-item>
        <el-descriptions-item label="创建者">{{ detailData.creatorName }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ parseTime(detailData.createTime) }}</el-descriptions-item>
        <el-descriptions-item label="最后更新">{{ parseTime(detailData.lastUpdateTime) }}</el-descriptions-item>
        <el-descriptions-item label="我的权限" v-if="detailData.permission">
          <el-tag :type="getPermissionTagType(detailData.permission.permissionType)">
            {{ getPermissionText(detailData.permission.permissionType) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="描述" :span="2">{{ detailData.description }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ detailData.remark }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script setup name="MyKnowledgeBase">
import { listMyKnowledgeBase, listCreatedKnowledgeBase, listSharedKnowledgeBase, getMyKnowledgeBase, delMyKnowledgeBase, addMyKnowledgeBase, updateMyKnowledgeBase } from "@/api/knowledge/my";

const { proxy } = getCurrentInstance();
const { sys_normal_disable } = proxy.useDict('sys_normal_disable');

const knowledgeBaseList = ref([]);
const open = ref(false);
const detailOpen = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const activeTab = ref("all");
const detailData = ref({});

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    name: null,
    type: null,
    status: null
  },
  rules: {
    name: [
      { required: true, message: "知识库名称不能为空", trigger: "blur" }
    ],
    type: [
      { required: true, message: "知识库类型不能为空", trigger: "change" }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询知识库列表 */
function getList() {
  loading.value = true;
  let apiCall;
  
  switch (activeTab.value) {
    case "created":
      apiCall = listCreatedKnowledgeBase(queryParams.value);
      break;
    case "shared":
      apiCall = listSharedKnowledgeBase(queryParams.value);
      break;
    default:
      apiCall = listMyKnowledgeBase(queryParams.value);
  }
  
  apiCall.then(response => {
    knowledgeBaseList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 标签页切换
function handleTabClick(tab) {
  activeTab.value = tab.name;
  queryParams.value.pageNum = 1;
  getList();
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    name: null,
    description: null,
    type: "general",
    status: "0",
    remark: null
  };
  proxy.resetForm("knowledgeBaseRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加知识库";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const id = row.id || ids.value[0];
  getMyKnowledgeBase(id).then(response => {
    form.value = response.data.knowledgeBase;
    open.value = true;
    title.value = "修改知识库";
  });
}

/** 详情按钮操作 */
function handleDetail(row) {
  const id = row.id;
  getMyKnowledgeBase(id).then(response => {
    detailData.value = response.data.knowledgeBase;
    detailData.value.permission = response.data.permission;
    detailOpen.value = true;
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["knowledgeBaseRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateMyKnowledgeBase(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addMyKnowledgeBase(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除知识库编号为"' + _ids + '"的数据项？').then(function() {
    return delMyKnowledgeBase(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('knowledge/my/export', {
    ...queryParams.value
  }, `my_knowledge_base_${new Date().getTime()}.xlsx`)
}

/** 格式化字节大小 */
function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/** 获取权限类型文本 */
function getPermissionText(permissionType) {
  switch (permissionType) {
    case 'read': return '只读';
    case 'write': return '读写';
    case 'admin': return '管理员';
    default: return '未知';
  }
}

/** 获取权限标签类型 */
function getPermissionTagType(permissionType) {
  switch (permissionType) {
    case 'read': return 'info';
    case 'write': return 'warning';
    case 'admin': return 'success';
    default: return '';
  }
}

/** 初始化 */
onMounted(() => {
  getList();
});
</script>
